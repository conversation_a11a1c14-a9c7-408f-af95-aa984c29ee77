package com.chic.quality.domain.service.impl;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chic.commons.util.StringUtils;
import com.chic.quality.apis.model.dto.*;
import com.chic.quality.apis.model.vo.RulesetVo;
import com.chic.quality.apis.protocol.mapping.DataSetMapping;
import com.chic.quality.domain.database.entity.*;
import com.chic.quality.domain.database.mapper.RuleMapper;
import com.chic.quality.domain.service.*;
import com.chic.quality.infrastructure.general.constants.RuleType;
import com.chic.quality.infrastructure.general.constants.TemplateTypeEnum;
import com.chic.quality.infrastructure.general.constants.ValidateMetricEnum;
import com.chic.quality.infrastructure.general.converter.SqlTemplateConverter;
import com.chic.quality.infrastructure.general.util.RegexUtils;
import com.chic.quality.infrastructure.general.util.SqlParserUtil;
import com.googlecode.aviator.AviatorEvaluator;
import lombok.extern.slf4j.Slf4j;
import com.chic.quality.infrastructure.general.constants.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Date;

/**
 * <p>
 * 质量规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
//@Slf4j(topic = "xxl-job-executor")
@Service
public class RuleExecuteServiceImpl extends ServiceImpl<RuleMapper, Rule> implements RuleExecuteService {
    // 规则执行日志记录器
    private static final Logger ruleLogger = LoggerFactory.getLogger("quality-rule-execution-job");

    @Autowired
    private DataSetMapping dataSetMapping;
    @Autowired
    private RuleTemplateService ruleTemplateService;
    @Autowired
    private TemplateStatisticInputMetaService templateStatisticInputMetaService;
    @Autowired
    private DataSetService dataSetService;
    @Autowired
    private SparkEngineService sparkEngineService;
    @Autowired
    private RuleValidateLogService ruleValidateLogService;
    @Autowired
    private TemplateMidTableInputMetaService templateMidTableInputMetaService;
    @Autowired
    private DataSetMetaService dataSetMetaService;
    @Autowired
    private RuleExceptionDataService ruleExceptionDataService;

    @Override
    public boolean executeRule(RuleExecuteContext context) {
        // 确保MDC中有批次号和规则ID
        String batchNumber = context.getBatchNumber();
        Long ruleId = context.getRuleDTO().getId();
       
        // 如果MDC中没有设置相应的值，设置它们
        if (MDC.get("batchNumber") == null && batchNumber != null) {
            MDC.put("batchNumber", batchNumber);
        }
        if (MDC.get("ruleId") == null && ruleId != null) {
            MDC.put("ruleId", ruleId.toString());
        }
       
        ruleLogger.info("开始执行规则 - 规则ID={}, 规则名称={}, 批次号={}, 业务日期={}, 调度任务={}", 
                ruleId, context.getRuleDTO().getRuleName(), batchNumber,context.getBizDate(),context.getRuleSchedule().getScheduleName());
        
        context.setStartTime(LocalDateTime.now());
        RuleDTO rule = context.getRuleDTO();
        RuleScheduleInfo schedule = context.getRuleSchedule();
        boolean flag = true;
        try {
            
            
            RuleTemplate template = ruleTemplateService.getById(rule.getTemplateId());
            if (template == null) {
                ruleLogger.error("规则模板不存在: ruleId={}, templateId={}", rule.getId(), rule.getTemplateId());
                return false;
            }
            ruleLogger.info("获取规则模板: ruleId={}, templateId={}, templateName={}", rule.getId(), rule.getTemplateId(),template.getTemplateName());
            
            TemplateStatisticInputMeta statisticInputMeta = templateStatisticInputMetaService.getOne(Wrappers.<TemplateStatisticInputMeta>lambdaQuery()
                    .eq(TemplateStatisticInputMeta::getTemplateId, rule.getTemplateId()));
            List<TemplateMidTableInputMeta> templateMidTableInputMetas = templateMidTableInputMetaService.listByTemplateId(rule.getTemplateId());
            

            List<RuleValidateObject> ruleValidateObjects = JSON.parseArray(rule.getValidateObjects(), RuleValidateObject.class);
            List<Long> colMetaDataIds = ruleValidateObjects.stream().map(RuleValidateObject::getColMetaDataId).collect(Collectors.toList());
            
            DataSetDTO dataSetDto = dataSetService.selectDsAndCol(rule.getWatchId(), colMetaDataIds);
            context.setRuleDTO(rule);
            context.setRuleTemplate(template);
            context.setDataSetDto(dataSetDto);
            context.setStatisticInputMeta(statisticInputMeta);
            context.setTemplateMidTableInputMetas(templateMidTableInputMetas);
            context.setRuleValidateObjects(ruleValidateObjects);


            String sqlText = parsePlaceholders(dataSetDto.getSqlText(), schedule, dataSetDto.getDataSource().getType(), context.getBizDate());
            dataSetDto.setSqlText(sqlText);
            ruleLogger.info("解析校验对象SQL占位符: ruleId={}, sqlText=\n{}", rule.getId(), sqlText);
            
            if(TemplateTypeEnum.DOUBLE_TABLE_FIELD_VALUE_COMPARE.getCode().equals(template.getTemplateType())){
                setCompareObject(context, rule, dataSetDto);
                ruleLogger.info("解析比较对象SQL占位符: ruleId={}, sqlText=\n{}", rule.getId(), context.getCompareDataSetDto().getSqlText());
            }

            SqlTemplateConverter sqlTemplateConverter = new SqlTemplateConverter();
            Object errorNumObject, totalNumObject=null;
            
            // 相同数据源在目标数据库处理；不同数据源核对，在spark引擎中处理
            if(!context.getSameDataSource()){
                ruleLogger.info("不同数据源处理: ruleId={}, 使用Spark引擎", rule.getId());
                
                Map<String,String> map = sparkEngineService.createTempViewReturnMap(dataSetDto.getDataSource(), dataSetDto.getSqlText());
                dataSetDto.setSqlText(map.get(Constants.VIEW_NAME));
                dataSetDto.setCreateViewSql(map.get(Constants.CREATE_VIEW_SQL));

                DataSetDTO compareDataSetDto = context.getCompareDataSetDto();
                Map<String,String> map2 = sparkEngineService.createTempViewReturnMap(compareDataSetDto.getDataSource(), compareDataSetDto.getSqlText());
                compareDataSetDto.setSqlText(map2.get(Constants.VIEW_NAME));
                compareDataSetDto.setCreateViewSql(map2.get(Constants.CREATE_VIEW_SQL));

                context.setExecuteEngine(Constants.HIVE);
                String sql = sqlTemplateConverter.convertStatisticErrorNumSql(context);
                ruleLogger.info("不同数据源执行数据统计SQL: ruleId={}, sql={}", rule.getId(), sql);
                
                errorNumObject = sparkEngineService.queryForObject(sql);
                context.setErrorNumSql(sql);
            } else {
                ruleLogger.info("相同数据源处理: ruleId={}, dataSource={}", rule.getId(), dataSetDto.getDataSource().getType());
                context.setExecuteEngine(dataSetDto.getDataSource().getType());
                String sql = sqlTemplateConverter.convertStatisticErrorNumSql(context);
                
                ruleLogger.info("相同数据源执行数据统计SQL: ruleId={},sql={}", rule.getId(),sql);
                errorNumObject = sparkEngineService.queryForObject(dataSetDto.getDataSource(), sql);

//                String statisticTotalNumSql = sqlTemplateConverter.convertStatisticTotalNumSql(
//                        dataSetDto.getSqlText(), rule.getFilter(), template, statisticInputMeta);
//                ruleLogger.info("执行总数据统计SQL: ruleId={}", rule.getId());
//                totalNumObject = sparkEngineService.queryForObject(dataSetDto.getDataSource(), statisticTotalNumSql);

                context.setErrorNumSql(sql);
                //context.setTotalNumSql(statisticTotalNumSql);
            }
            Long errorNum = convertToLong(errorNumObject);
            Long totalNum = convertToLong(totalNumObject);
            ruleLogger.info("统计结果: ruleId={}, 总数据条数={}, 校验结果数据条数={}", rule.getId(), totalNum, errorNum);

            
            this.executeValidate(context, totalNum, errorNum);

            // 添加关键步骤的日志记录
            ruleLogger.info("规则执行完成 - 规则ID={}, 规则名称={}, 批次号={}, 验证结果={}", 
                    ruleId, context.getRuleDTO().getRuleName(), batchNumber, 
                    context.getValidateStatus() ? "校验不通过" : "校验通过");
            
            // 归档
            if(context.getValidateStatus()){
                LocalDateTime startTime = LocalDateTime.now();
                ruleLogger.info("归档异常数据: ruleId={}, rowDataSql={}", rule.getId(), context.getRowDataSql());
                List<Map<String, Object>> rowData;
                if(context.getExecuteEngine().equals(Constants.HIVE)){
                    rowData = sparkEngineService.queryForList(context.getRowDataSql());
                }else{
                    rowData = sparkEngineService.queryForList(dataSetDto.getDataSource(),context.getRowDataSql());
                }
                ruleLogger.info("归档异常数据: ruleId={}, rowData-size={}", rule.getId(),  rowData != null ? rowData.size() : 0);
                if(!CollectionUtils.isEmpty(rowData)){
                    rule.setRowData(rowData);
                    rule.setBatchNumber(context.getBatchNumber());
                    // 不同数据源rowDataSql需要拼接createViewSql
                    if(!context.getSameDataSource()){
                        String appendRowDataSql = context.getDataSetDto().getCreateViewSql()+";\n"
                        + context.getCompareDataSetDto().getCreateViewSql()+";\n"
                        + context.getRowDataSql();
                        rule.setRowDataSql(appendRowDataSql.replaceAll(Constants.PASSWORD_MASK_PATTERN, Constants.PASSWORD_MASK_REPLACEMENT));
                    }else{
                        rule.setRowDataSql(context.getRowDataSql());
                    }
                    RuleExceptionData ruleExceptionData = new RuleExceptionData(context.getBatchNumber(),rule.getId(), JSON.toJSONString(rowData));
                    ruleExceptionDataService.save(ruleExceptionData);
                }
                ruleLogger.info("归档异常数据完成: ruleId={}, excutetime={}ms", rule.getId(), Duration.between(startTime, LocalDateTime.now()).toMillis()  );
            }
            // 记录日志
            ruleLogger.info("记录规则执行日志: ruleId={}", rule.getId());
            RuleValidateLog ruleValidateLog = completeRuleValidateLog(context, errorNum, totalNum);
            ruleValidateLogService.save(ruleValidateLog);
            rule.setErrorMessage(null);
        } catch (Exception e) {
            String message = e.getMessage() != null ? RegexUtils.maskPassword(e.getMessage()) : "SQL异常";
            rule.setErrorMessage(message.length()>1500?message.substring(0,1500):message);
            flag =  false;
            ruleLogger.error("规则执行异常 - 规则ID={}, 规则名称={} , 错误信息={}", rule.getId(), rule.getRuleName(), message);
        }
        return flag;
    }



    private void setCompareObject(RuleExecuteContext context, RuleDTO rule, DataSetDTO dataSetDto) {
        // 比较对象
        RuleCompareObject compareObject;
        if(StringUtils.isNotBlank(rule.getCompareObject())) {
            compareObject = JSON.parseObject(rule.getCompareObject(), RuleCompareObject.class);
            // 判断是否是同一个数据集
            DataSetMeta compareDataSetMetaDto = dataSetMetaService.getById(compareObject.getColMetaDataId());
            DataSetDTO compareDataSetDto;
            if(dataSetDto.getId() == compareDataSetMetaDto.getDataSetId()) {
                // 同一个数据集，直接使用原来的数据集对象即可
                compareDataSetDto = dataSetMapping.toDto(dataSetDto);
            }else{
                compareDataSetDto = dataSetService.selectDsAndDataSet(compareDataSetMetaDto.getDataSetId());
            }
            List<DataSetMeta> dataSetMetaList = new ArrayList<>();
            dataSetMetaList.add(compareDataSetMetaDto);
            compareDataSetDto.setDataSetMetaList(dataSetMetaList);

            // 是否同一个数据源
            if(dataSetDto.getDataSource().getId() != compareDataSetDto.getDataSource().getId()) {
                context.setSameDataSource(false);
            }
            String sqlText = parsePlaceholders(compareDataSetDto.getSqlText(),
                    context.getRuleSchedule(), compareDataSetDto.getDataSource().getType(), context.getBizDate());
            compareDataSetDto.setSqlText(sqlText);

            context.setCompareDataSetDto(compareDataSetDto);
            context.setRuleCompareObject(compareObject);
        }
    }

    private RuleValidateLog completeRuleValidateLog(RuleExecuteContext context, Long errorNum, Long totalNum) {
        RulesetVo ruleset = context.getRuleset();
        RuleValidateLog ruleValidateLog = new RuleValidateLog();
        RuleDTO rule = context.getRuleDTO();
        RuleTemplate ruleTemplate = context.getRuleTemplate();
        ruleValidateLog.setRuleId(rule.getId());
        ruleValidateLog.setRuleName(rule.getRuleName());
        ruleValidateLog.setRulesetId(ruleset.getId());
        ruleValidateLog.setRulesetName(ruleset.getName());

        ruleValidateLog.setJobId(context.getJobId());
        ruleValidateLog.setStartTime(context.getStartTime());
        ruleValidateLog.setEndTime(LocalDateTime.now());
        ruleValidateLog.setExecuteTime(calculateExecuteTime(ruleValidateLog));
        ruleValidateLog.setErrorNum(errorNum);
        ruleValidateLog.setTotalNum(totalNum);
        ruleValidateLog.setTotalSql(context.getTotalNumSql());
        ruleValidateLog.setValidateSql(context.getErrorNumSql());
        ruleValidateLog.setErrorData(null);
        ruleValidateLog.setValidateStatus(context.getValidateStatus());
        ruleValidateLog.setValidateCondition(context.getValidateCondition());
        ruleValidateLog.setTryRun(context.isTryRun());
        ruleValidateLog.setScheduleId(context.getRuleSchedule().getId());
        ruleValidateLog.setScheduleName(context.getRuleSchedule().getScheduleName());
        ruleValidateLog.setValidateRange(null);
        ruleValidateLog.setRuleType(rule.getRuleType());
        ruleValidateLog.setRuleTypeName(RuleType.getByCode(rule.getRuleType()).getDescription());
        ruleValidateLog.setRuleTemplateId(ruleTemplate.getId());
        ruleValidateLog.setRuleTemplateName(ruleTemplate.getTemplateName());
        ruleValidateLog.setBatchNumber(context.getBatchNumber());
        // 设置校验对象
        setValidateObject(context, ruleset, ruleValidateLog);
        // 设置校验结果
        ruleValidateLog.setRuleValidateResults(JSON.toJSONString(context.getRuleValidateResults()));

        // 设置规则告警信息
        rule.setAlertMsg(String.format(ruleTemplate.getAlertTemplate(),ruleValidateLog.getRuleName(),ruleValidateLog.getValidateObject()));

        return ruleValidateLog;

    }

    private void setValidateObject(RuleExecuteContext context, RulesetVo ruleset, RuleValidateLog ruleValidateLog) {
        DataSetDTO dataSetDto = context.getDataSetDto();
        List<DataSetMeta> dataSetMetaList = dataSetDto.getDataSetMetaList();
        if(dataSetMetaList.size() > 0){
            String columns = dataSetMetaList.stream().map(DataSetMeta::getColumnAlias).collect(Collectors.joining(","));
            ruleValidateLog.setValidateObject(columns);
            ruleValidateLog.setValidateType(Constants.VALIDATE_TYPE_FIELD);
        }else{
            ruleValidateLog.setValidateObject(ruleset.getName());
            ruleValidateLog.setValidateType(Constants.VALIDATE_TYPE_TABLE);
        }
    }

    private Long calculateExecuteTime(RuleValidateLog ruleValidateLog) {
        Duration duration = Duration.between(ruleValidateLog.getStartTime(), ruleValidateLog.getEndTime());
        return duration.toMillis()/1000;
    }

    private String parsePlaceholders(String watch, RuleScheduleInfo schedule,String dbType,String bizDate) {
        LocalDate localDate = null;
        if(StringUtils.isNotBlank(bizDate)){
            localDate = LocalDate.parse(bizDate, DatePattern.NORM_DATE_FORMATTER);
        }
        return SqlParserUtil.parseDatePlaceholders(watch, localDate,dbType);
    }

    private void executeValidate(RuleExecuteContext context, Long totalNum, Long errorNum) {
        String validateConditionStr = context.getRuleDTO().getValidateCondition();
        if (StringUtils.isEmpty(validateConditionStr)) {
            context.setValidateStatus(true);
            return;
        }
        ValidateCondition validateCondition = JSON.parseObject(validateConditionStr, ValidateCondition.class);
        Object metricValue = null;
        if(ValidateMetricEnum.NORMAL_NUMBER.getCode().equals(validateCondition.getMetric())){
            metricValue = totalNum - errorNum;
        }else if(ValidateMetricEnum.NORMAL_RATE.getCode().equals(validateCondition.getMetric())){
            metricValue = (double)(totalNum- errorNum/ totalNum)*100;
        }else if(ValidateMetricEnum.ERROR_NUMBER.getCode().equals(validateCondition.getMetric())){
            metricValue = errorNum;
        }else if(ValidateMetricEnum.ERROR_RATE.getCode().equals(validateCondition.getMetric())){
            metricValue = (double)errorNum/ totalNum*100;
        }else if(ValidateMetricEnum.STATISTICS.getCode().equals(validateCondition.getMetric())){
            metricValue = totalNum;
        }
        // 操作符为=时，需变为==；解决com.googlecode.aviator.exception.ExpressionRuntimeException: <Long, 1248314> can't be a left value for assignment.
        if(Constants.VALIDATE_OPERATOR_EQUAL.equals(validateCondition.getOperator())){
            validateCondition.setOperator(Constants.VALIDATE_OPERATOR_EQUAL_REPLACEMENT);
        }

        String expression = String.format(Constants.VALIDATE_EXPRESSION, metricValue, validateCondition.getOperator(), validateCondition.getValue());
        Boolean validateStatus = (Boolean)AviatorEvaluator.execute(expression);
        context.setValidateCondition(validateConditionStr);
        context.setValidateStatus(validateStatus);

        setRuleValidateResults(context, validateCondition, metricValue);
    
    }

    private void setRuleValidateResults(RuleExecuteContext context, ValidateCondition validateCondition,
            Object metricValue) {
        RuleValidateResult ruleValidateResult = new RuleValidateResult();
        ruleValidateResult.setValidateTime(new Date());
        ruleValidateResult.setMetricName(ValidateMetricEnum.getCompareTypeName(validateCondition.getMetric()));
        ruleValidateResult.setMetricValue(metricValue);
        
        List<RuleValidateResult> ruleValidateResults = new ArrayList<>();
        ruleValidateResults.add(ruleValidateResult);
        context.setRuleValidateResults(ruleValidateResults);
    }

    public static void main(String[] args) {
        String expression = String.format(Constants.VALIDATE_EXPRESSION, 10, ">", 5.02);
        Boolean validateResult = (Boolean)AviatorEvaluator.execute(expression);
        System.out.println("校验结果：" + validateResult);
    }

    /**
     * 将对象转换为Long类型
     * @param obj 需要转换的对象
     * @return 转换后的Long值，如果转换失败则返回0L
     */
    private Long convertToLong(Object obj) {
        if (obj == null) {
            return 0L;
        }
        
        if (obj instanceof Long) {
            return (Long) obj;
        } else if (obj instanceof Integer) {
            return ((Integer) obj).longValue();
        } else if (obj instanceof java.math.BigDecimal) {
            return ((java.math.BigDecimal) obj).longValue();
        } else {
            // 尝试转换为字符串并解析为Long
            try {
                return Long.parseLong(obj.toString());
            } catch (Exception e) {
                ruleLogger.warn("无法将对象转换为Long类型: {}", obj);
                return 0L;
            }
        }
    }

}
