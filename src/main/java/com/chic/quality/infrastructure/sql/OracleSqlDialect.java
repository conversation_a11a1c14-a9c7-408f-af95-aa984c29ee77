package com.chic.quality.infrastructure.sql;

import org.apache.calcite.avatica.util.Casing;
import org.apache.calcite.sql.SqlCall;
import org.apache.calcite.sql.SqlDialect;
import org.apache.calcite.sql.SqlKind;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.SqlWriter;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.sql.pretty.SqlPrettyWriter;



/**
 * <AUTHOR>
 * @classname OracleSqlDialect
 * @description TODO
 * @date 2025/2/25 10:01
 */
public class OracleSqlDialect extends SqlDialect {
    public static final SqlDialect DEFAULT = new OracleSqlDialect(
            EMPTY_CONTEXT
                    .withIdentifierQuoteString("\"")      // Oracle 使用双引号
                    .withUnquotedCasing(Casing.TO_UPPER)  // 未引用的标识符转为大写
                    .withQuotedCasing(Casing.UNCHANGED)   // 引用的标识符保持不变
                    .withCaseSensitive(false)             // Oracle 默认不区分大小写
    );

    public OracleSqlDialect(Context context) {
        super(context);
    }

    @Override
    public boolean requiresAliasForFromItems() {
        // Oracle 不需要 AS 关键字
        return false;
    }

    @Override
    public String quoteIdentifier(String val) {
        // 对于需要引号的标识符，强制使用双引号
        if (identifierNeedsQuote(val)) {
            return "\"" + val + "\"";
        }
        return val;
    }

    @Override
    public boolean identifierNeedsQuote(String val) {
        // 判断标识符是否需要引号
        if (val == null || val.isEmpty()) {
            return false;
        }

        // 包含中文字符需要引号
        if (containsChinese(val)) {
            return true;
        }

        // 包含特殊字符或空格需要引号
        if (containsSpecialChars(val)) {
            return true;
        }

        // Oracle关键字需要引号
        if (isOracleKeyword(val)) {
            return true;
        }

        // 如果标识符以数字开头需要引号
        if (Character.isDigit(val.charAt(0))) {
            return true;
        }

        return false;
    }

    /**
     * 检查字符串是否包含中文字符
     */
    private boolean containsChinese(String str) {
        for (char c : str.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fff) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查字符串是否包含特殊字符
     */
    private boolean containsSpecialChars(String str) {
        // 检查是否包含空格、特殊符号等
        return str.matches(".*[\\s\\-\\+\\*\\/\\(\\)\\[\\]\\{\\}\\,\\;\\.\\:\\?\\!\\@\\#\\$\\%\\^\\&\\=\\<\\>\\|\\\\].*");
    }

    /**
     * 检查是否为Oracle关键字（简化版本）
     */
    private boolean isOracleKeyword(String str) {
        String upperStr = str.toUpperCase();
        // 这里只列出一些常见的Oracle关键字，实际使用中可以扩展
        String[] keywords = {
            "SELECT", "FROM", "WHERE", "INSERT", "UPDATE", "DELETE", "CREATE", "DROP",
            "ALTER", "TABLE", "INDEX", "VIEW", "GRANT", "REVOKE", "COMMIT", "ROLLBACK",
            "ORDER", "GROUP", "HAVING", "UNION", "JOIN", "INNER", "LEFT", "RIGHT", "FULL",
            "AS", "AND", "OR", "NOT", "NULL", "IS", "IN", "EXISTS", "BETWEEN", "LIKE"
        };

        for (String keyword : keywords) {
            if (keyword.equals(upperStr)) {
                return true;
            }
        }
        return false;
    }
    @Override
    public void unparseCall(SqlWriter writer, SqlCall call, int leftPrec, int rightPrec) {
        super.unparseCall(writer, call, leftPrec, rightPrec);
        /*if (call.getKind() == SqlKind.AS) {
            SqlNode[] operands = call.getOperandList().toArray(new SqlNode[0]);
            operands[0].unparse(writer, leftPrec, rightPrec);
            writer.print(""); // Oracle 使用空格而不是 AS
            operands[1].unparse(writer, leftPrec, rightPrec);
        } else {
            super.unparseCall(writer, call, leftPrec, rightPrec);
        }*/
    }

    public static void main(String[] args) {
        try {
            // 测试表别名
            testSqlFormatting(
                "select A.RISKCODE as 产品代码, sum(SUMPREMIUM)as 保费\n" +
                        "  from (select /*+ PARALLEL (8) */\n" +
                        "         A.CLASSCODE,\n" +
                        "         A.POLICYNO,\n" +
                        "         A.RISKCODE,\n" +
                        "         (NVL(G.NOVATGROSSPREMIUM, 0) *\n" +
                        "         DECODE(A.CURRENCY2,\n" +
                        "                 'CNY',\n" +
                        "                 1,\n" +
                        "                 DECODE(B.EXCHRATE, NULL, 1, B.EXCHRATE)) *\n" +
                        "         DECODE(A.COINSFLAG,\n" +
                        "                 1,\n" +
                        "                 DECODE(C.COINSRATE, NULL, 100, C.COINSRATE) / 100,\n" +
                        "                 DECODE(A.COINSFLAG,\n" +
                        "                        3,\n" +
                        "                        DECODE(C.COINSRATE, NULL, 100, C.COINSRATE) / 100,\n" +
                        "                        1))) AS SUMPREMIUM\n" +
                        "          from CHIC_FC_INS.PRPCOPYMAIN A\n" +
                        "          left join (SELECT EXCHDATE as EXCHDATE,\n" +
                        "                           BASECURRENCY,\n" +
                        "                           EXCHCURRENCY,\n" +
                        "                           EXCHRATE,\n" +
                        "                           (LEAD(EXCHDATE, 1, CURRENT_DATE)\n" +
                        "                            OVER(PARTITION BY BASECURRENCY ORDER BY EXCHDATE)) EXCHDATE_END\n" +
                        "                      FROM CHIC_FC_INS.PRPDEXCH\n" +
                        "                     WHERE EXCHCURRENCY = 'CNY') b\n" +
                        "            on A.CURRENCY2 = B.BASECURRENCY\n" +
                        "          left join CHIC_FC_INS.PRPCOPYITEMKIND G\n" +
                        "            on G.POLICYNO = A.POLICYNO\n" +
                        "           and a.endorseqno = G.endorseqno\n" +
                        "           and a.riskcode = G.riskcode\n" +
                        "           and a.endorseqno = '000'\n" +
                        "          left join CHIC_FC_INS.PRPCCOINS C\n" +
                        "            on A.POLICYNO = C.POLICYNO\n" +
                        "         WHERE a.endorseqno = '000'\n" +
                        "           and (C.PRINCIPALIND = '1' OR C.PRINCIPALIND IS NULL)\n" +
                        "           and A.UNDERWRITEENDDATE IS NOT NULL\n" +
                        "           AND (A.UNDERWRITEENDDATE >= TO_DATE('2024-07-01 00:00:00', 'YYYY-MM-DD hh24:mi:ss') AND\n" +
                        "               A.UNDERWRITEENDDATE <= TO_DATE('2024-07-01 23:59:59', 'YYYY-MM-DD hh24:mi:ss') AND\n" +
                        "               A.UNDERWRITEENDDATE >= A.STARTDATE OR\n" +
                        "               (A.STARTDATE >= TO_DATE('2024-07-01 00:00:00', 'YYYY-MM-DD hh24:mi:ss') AND\n" +
                        "             A.STARTDATE <=  TO_DATE('2024-07-01 23:59:59', 'YYYY-MM-DD hh24:mi:ss') AND\n" +
                        "               A.STARTDATE > A.UNDERWRITEENDDATE))\n" +
                        "           AND ((A.UNDERWRITEENDDATE >= A.STARTDATE AND\n" +
                        "               A.UNDERWRITEENDDATE > B.EXCHDATE AND\n" +
                        "               A.UNDERWRITEENDDATE <= B.EXCHDATE_END) OR\n" +
                        "               (A.UNDERWRITEENDDATE <= A.STARTDATE AND\n" +
                        "               A.STARTDATE > B.EXCHDATE AND A.STARTDATE <= B.EXCHDATE_END))\n" +
                        "           AND A.CLASSCODE IN ('26', '27')\n" +
                        "           and SUBSTR(A.RISKCODE, 1, 2) NOT IN\n" +
                        "               ('31', '32', '25', '05', '40')\n" +
                        "           and (A.FREEPRODFLAG <> '1' OR A.FREEPRODFLAG IS NULL)\n" +
                        "           and A.UNDERWRITEFLAG IN ('1', '3')\n" +
                        "        union all\n" +
                        "        select /*+ PARALLEL (8) */\n" +
                        "         A.CLASSCODE,\n" +
                        "         A.POLICYNO,\n" +
                        "         A.RISKCODE,\n" +
                        "         (NVL(A.netPremium2, 0) *\n" +
                        "         DECODE(A.CURRENCY2,\n" +
                        "                 'CNY',\n" +
                        "                 1,\n" +
                        "                 DECODE(B.EXCHRATE, NULL, 1, B.EXCHRATE)) *\n" +
                        "         DECODE(A.COINSFLAG,\n" +
                        "                 1,\n" +
                        "                 DECODE(C.COINSRATE, NULL, 100, C.COINSRATE) / 100,\n" +
                        "                 DECODE(A.COINSFLAG,\n" +
                        "                        3,\n" +
                        "                        DECODE(C.COINSRATE, NULL, 100, C.COINSRATE) / 100,\n" +
                        "                        1))) AS SUMPREMIUM\n" +
                        "          from CHIC_FC_INS.PRPCOPYMAIN A\n" +
                        "          left join (SELECT EXCHDATE as EXCHDATE,\n" +
                        "                            BASECURRENCY,\n" +
                        "                            EXCHCURRENCY,\n" +
                        "                            EXCHRATE,\n" +
                        "                            (LEAD(EXCHDATE, 1, CURRENT_DATE)\n" +
                        "                             OVER(PARTITION BY BASECURRENCY ORDER BY EXCHDATE)) EXCHDATE_END\n" +
                        "                       FROM CHIC_FC_INS.PRPDEXCH\n" +
                        "                      WHERE EXCHCURRENCY = 'CNY') b\n" +
                        "            on A.CURRENCY2 = B.BASECURRENCY\n" +
                        "          left join CHIC_FC_INS.PRPCCOINS C\n" +
                        "            on A.POLICYNO = C.POLICYNO\n" +
                        "         WHERE a.endorseqno = '000'\n" +
                        "           and (C.PRINCIPALIND = '1' OR C.PRINCIPALIND IS NULL)\n" +
                        "           and A.UNDERWRITEENDDATE IS NOT NULL\n" +
                        "           AND (A.UNDERWRITEENDDATE >= TO_DATE('2024-07-01 00:00:00', 'YYYY-MM-DD hh24:mi:ss') AND\n" +
                        "              A.UNDERWRITEENDDATE <= TO_DATE('2024-07-01 23:59:59', 'YYYY-MM-DD hh24:mi:ss') AND\n" +
                        "               A.UNDERWRITEENDDATE >= A.STARTDATE OR\n" +
                        "               (A.STARTDATE >= TO_DATE('2024-07-01 00:00:00', 'YYYY-MM-DD hh24:mi:ss') AND\n" +
                        "             A.STARTDATE <=  TO_DATE('2024-07-01 23:59:59', 'YYYY-MM-DD hh24:mi:ss') AND\n" +
                        "               A.STARTDATE > A.UNDERWRITEENDDATE))\n" +
                        "           AND ((A.UNDERWRITEENDDATE >= A.STARTDATE AND\n" +
                        "               A.UNDERWRITEENDDATE > B.EXCHDATE AND\n" +
                        "               A.UNDERWRITEENDDATE <= B.EXCHDATE_END) OR\n" +
                        "               (A.UNDERWRITEENDDATE <= A.STARTDATE AND\n" +
                        "               A.STARTDATE > B.EXCHDATE AND A.STARTDATE <= B.EXCHDATE_END))\n" +
                        "           AND A.CLASSCODE not IN ('26', '27', '05')\n" +
                        "           and SUBSTR(A.RISKCODE, 1, 2) NOT IN\n" +
                        "               ('31', '32', '25', '05', '40')\n" +
                        "           and (A.FREEPRODFLAG <> '1' OR A.FREEPRODFLAG IS NULL)\n" +
                        "           and A.UNDERWRITEFLAG IN ('1', '3')\n" +
                        "        union all\n" +
                        "        SELECT /*+ PARALLEL (8) */\n" +
                        "         CMO.CLASSCODE,\n" +
                        "         CMO.POLICYNO,\n" +
                        "         CMO.RISKCODE,\n" +
                        "         (NVL(PCIK.CHGNOVATGROSSPREMIUM, 0) *\n" +
                        "         DECODE(CMO.CURRENCY2,\n" +
                        "                 'CNY',\n" +
                        "                 1,\n" +
                        "                 DECODE(B.EXCHRATE, NULL, 1, B.EXCHRATE)) *\n" +
                        "         DECODE(CMO.COINSFLAG,\n" +
                        "                 1,\n" +
                        "                 DECODE(C.COINSRATE, NULL, 100, C.COINSRATE) / 100,\n" +
                        "                 DECODE(CMO.COINSFLAG,\n" +
                        "                        3,\n" +
                        "                        DECODE(C.COINSRATE, NULL, 100, C.COINSRATE) / 100,\n" +
                        "                        1))) AS SUMPREMIUM\n" +
                        "          FROM CHIC_FC_INS.PRPPHEAD A\n" +
                        "         INNER JOIN CHIC_FC_INS.PRPPMAIN CMO\n" +
                        "            ON A.ENDORSENO = CMO.ENDORSENO\n" +
                        "          LEFT JOIN (SELECT EXCHDATE AS EXCHDATE,\n" +
                        "                            BASECURRENCY,\n" +
                        "                            EXCHCURRENCY,\n" +
                        "                            EXCHRATE,\n" +
                        "                            (LEAD(EXCHDATE, 1, CURRENT_DATE)\n" +
                        "                             OVER(PARTITION BY BASECURRENCY ORDER BY EXCHDATE)) EXCHDATE_END\n" +
                        "                       FROM CHIC_FC_INS.PRPDEXCH\n" +
                        "                      WHERE EXCHCURRENCY = 'CNY') B\n" +
                        "            ON CMO.CURRENCY2 = B.BASECURRENCY\n" +
                        "          LEFT JOIN CHIC_FC_INS.PRPCCOINS C\n" +
                        "            ON A.POLICYNO = C.POLICYNO\n" +
                        "          LEFT JOIN CHIC_FC_INS.PRPPITEMKIND PCIK\n" +
                        "            ON PCIK.APPLYNO = A.APPLYNO\n" +
                        "         WHERE (CMO.FREEPRODFLAG <> '1' OR CMO.FREEPRODFLAG IS NULL)\n" +
                        "           AND CMO.CLASSCODE IN ('26', '27')\n" +
                        "           AND ((A.UNDERWRITEENDDATE >= A.VALIDDATE AND\n" +
                        "               A.UNDERWRITEENDDATE > B.EXCHDATE AND\n" +
                        "               A.UNDERWRITEENDDATE <= B.EXCHDATE_END) OR\n" +
                        "               (A.UNDERWRITEENDDATE <= A.VALIDDATE AND\n" +
                        "               A.VALIDDATE > B.EXCHDATE AND A.VALIDDATE <= B.EXCHDATE_END))\n" +
                        "           AND (C.PRINCIPALIND = '1' OR C.PRINCIPALIND IS NULL)\n" +
                        "           AND (A.UNDERWRITEENDDATE >= TO_DATE('2024-07-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') AND\n" +
                        "             A.UNDERWRITEENDDATE <=  TO_DATE('2024-07-01 23:59:59', 'YYYY-MM-DD HH24:MI:SS') AND\n" +
                        "               A.UNDERWRITEENDDATE >= A.VALIDDATE OR\n" +
                        "               (A.VALIDDATE >=  TO_DATE('2024-07-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') AND\n" +
                        "              A.VALIDDATE <= TO_DATE('2024-07-01 23:59:59', 'YYYY-MM-DD HH24:MI:SS') AND\n" +
                        "               A.VALIDDATE > A.UNDERWRITEENDDATE))\n" +
                        "           AND B.EXCHCURRENCY = 'CNY'\n" +
                        "           AND A.UNDERWRITEFLAG IN ('1', '3')\n" +
                        "           and SUBSTR(A.RISKCODE, 1, 2) NOT IN\n" +
                        "               ('31', '32', '25', '05', '40')\n" +
                        "        union ALL\n" +
                        "        SELECT /*+ PARALLEL (8) */\n" +
                        "         CMO.CLASSCODE,\n" +
                        "         CMO.POLICYNO,\n" +
                        "         CMO.RISKCODE,\n" +
                        "         (NVL(CMO.chgnetPremium2, 0) *\n" +
                        "         DECODE(CMO.CURRENCY2,\n" +
                        "                 'CNY',\n" +
                        "                 1,\n" +
                        "                 DECODE(B.EXCHRATE, NULL, 1, B.EXCHRATE)) *\n" +
                        "         DECODE(CMO.COINSFLAG,\n" +
                        "                 1,\n" +
                        "                 DECODE(C.COINSRATE, NULL, 100, C.COINSRATE) / 100,\n" +
                        "                 DECODE(CMO.COINSFLAG,\n" +
                        "                        3,\n" +
                        "                        DECODE(C.COINSRATE, NULL, 100, C.COINSRATE) / 100,\n" +
                        "                        1))) AS SUMPREMIUM\n" +
                        "          FROM CHIC_FC_INS.PRPPHEAD A\n" +
                        "         INNER JOIN CHIC_FC_INS.PRPPMAIN CMO\n" +
                        "            ON A.ENDORSENO = CMO.ENDORSENO\n" +
                        "          LEFT JOIN (SELECT EXCHDATE AS EXCHDATE,\n" +
                        "                            BASECURRENCY,\n" +
                        "                            EXCHCURRENCY,\n" +
                        "                            EXCHRATE,\n" +
                        "                            (LEAD(EXCHDATE, 1, CURRENT_DATE)\n" +
                        "                             OVER(PARTITION BY BASECURRENCY ORDER BY EXCHDATE)) EXCHDATE_END\n" +
                        "                       FROM CHIC_FC_INS.PRPDEXCH\n" +
                        "                      WHERE EXCHCURRENCY = 'CNY') B\n" +
                        "            ON CMO.CURRENCY2 = B.BASECURRENCY\n" +
                        "          LEFT JOIN CHIC_FC_INS.PRPCCOINS C\n" +
                        "            ON A.POLICYNO = C.POLICYNO\n" +
                        "         WHERE (CMO.FREEPRODFLAG <> '1' OR CMO.FREEPRODFLAG IS NULL)\n" +
                        "           AND CMO.CLASSCODE NOT IN ('26', '27', '05')\n" +
                        "           AND ((A.UNDERWRITEENDDATE >= A.VALIDDATE AND\n" +
                        "               A.UNDERWRITEENDDATE > B.EXCHDATE AND\n" +
                        "               A.UNDERWRITEENDDATE <= B.EXCHDATE_END) OR\n" +
                        "               (A.UNDERWRITEENDDATE <= A.VALIDDATE AND\n" +
                        "               A.VALIDDATE > B.EXCHDATE AND A.VALIDDATE <= B.EXCHDATE_END))\n" +
                        "           AND (C.PRINCIPALIND = '1' OR C.PRINCIPALIND IS NULL)\n" +
                        "           AND (A.UNDERWRITEENDDATE >=  TO_DATE('2024-07-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') AND\n" +
                        "               A.UNDERWRITEENDDATE <= TO_DATE('2024-07-01 23:59:59', 'YYYY-MM-DD HH24:MI:SS') AND\n" +
                        "               A.UNDERWRITEENDDATE >= A.VALIDDATE OR\n" +
                        "               (A.VALIDDATE >=  TO_DATE('2024-07-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') AND\n" +
                        "             A.VALIDDATE <=    TO_DATE('2024-07-01 23:59:59', 'YYYY-MM-DD HH24:MI:SS') AND\n" +
                        "               A.VALIDDATE > A.UNDERWRITEENDDATE))\n" +
                        "           AND B.EXCHCURRENCY = 'CNY'\n" +
                        "           AND A.UNDERWRITEFLAG IN ('1', '3')\n" +
                        "           and SUBSTR(A.RISKCODE, 1, 2) NOT IN\n" +
                        "               ('31', '32', '25', '05', '40')\n" +
                        ") a\n" +
                        " group by a.riskcode"
            );
            
        } catch (SqlParseException e) {
            e.printStackTrace();
        }
    }
    private static void testSqlFormatting(String sql) throws SqlParseException {
        System.out.println("\n原始SQL: " + sql);
        
        // 解析SQL
        SqlParser parser = SqlParser.create(sql);
        SqlNode sqlNode = parser.parseQuery();
        
        // 使用OracleSqlDialect格式化SQL
        SqlPrettyWriter writer = new SqlPrettyWriter(OracleSqlDialect.DEFAULT);
        //writer.setDialect(OracleSqlDialect.DEFAULT);
        sqlNode.unparse(writer, 0, 0);
        
        // 输出结果
        String formattedSql = writer.toSqlString().getSql();
        System.out.println("Oracle格式化SQL: " + formattedSql);
        System.out.println("----------------------------------------");
    }

}
