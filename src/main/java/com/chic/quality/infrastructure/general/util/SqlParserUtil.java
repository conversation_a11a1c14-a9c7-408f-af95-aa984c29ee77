package com.chic.quality.infrastructure.general.util;

import com.chic.commons.util.StringUtils;
import com.chic.quality.infrastructure.general.constants.Constants;
import com.chic.quality.infrastructure.metadata.ColumnMetadata;
import com.chic.quality.infrastructure.sql.OracleSqlDialect;

import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.avatica.util.Casing;
import org.apache.calcite.avatica.util.Quoting;
import org.apache.calcite.sql.*;
import org.apache.calcite.sql.fun.SqlStdOperatorTable;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.parser.SqlParser;

import org.apache.calcite.sql.parser.SqlParserPos;
import org.apache.calcite.sql.type.SqlTypeUtil;
import org.apache.calcite.sql.validate.SqlConformanceEnum;
import org.apache.calcite.sql.pretty.SqlPrettyWriter;
import org.apache.calcite.sql.SqlWriterConfig;
import org.apache.calcite.sql.SqlDialect;

import java.sql.JDBCType;
import java.sql.Types;
import java.time.LocalDate;
import java.util.*;

import static com.chic.quality.infrastructure.general.constants.Constants.DB_TYPE_MAP;

/**
 * <AUTHOR>
 * @classname SqlParserConfigUtil
 * @description TODO
 * @date 2024/12/11 20:59
 */
@Slf4j
public class SqlParserUtil {
    private static final String DEFAULT_TABLE_ALIAS = "t";
    private static final String ORACLE = "oracle";

    private static SqlParser.Config getDefaultConfig() {
        return SqlParser.config()
                .withQuotedCasing(Casing.UNCHANGED)
                .withUnquotedCasing(Casing.UNCHANGED)
                .withQuoting(Quoting.DOUBLE_QUOTE)       // 使用双引号引起标识符
                .withCaseSensitive(true) // 区分大小写
                .withConformance(SqlConformanceEnum.DEFAULT); // 宽松模式
    }
    private static SqlParser.Config getOracleConfig() {
        return SqlParser.config()
                .withQuotedCasing(Casing.UNCHANGED)       // 标识符（加引号的）转为大写
                .withUnquotedCasing(Casing.TO_UPPER)     // 标识符（不加引号的）转为大写
                .withQuoting(Quoting.DOUBLE_QUOTE)       // 使用双引号引起标识符
                .withCaseSensitive(false)                // 默认不区分大小写
                .withConformance(SqlConformanceEnum.ORACLE_12); // 使用较宽松的SQL符合性
    }
    private static SqlParser.Config getQuotingBackTickConfig() {
        return SqlParser.config()
                .withQuotedCasing(Casing.UNCHANGED)
                .withUnquotedCasing(Casing.UNCHANGED)
                .withQuoting(Quoting.BACK_TICK)
                .withCaseSensitive(true) // 区分大小写
                .withConformance(SqlConformanceEnum.DEFAULT); // 宽松模式
    }


    public static SqlNode getSqlNode(String sql) {
        SqlParser parser = SqlParser.create(sql,getDefaultConfig());
        SqlNode sqlNode;
        try {
            sqlNode = parser.parseQuery();
            log.info("SQL语法正确!");
        } catch (SqlParseException e) {
            log.error("SQL语法错误!", e);
            throw new RuntimeException("SQL语法错误:"+e.getMessage());
        }
        return sqlNode;
    }
    public static SqlNode getSqlNodeByDefault(String sql) {
        SqlParser parser = SqlParser.create(sql);
        SqlNode sqlNode;
        try {
            sqlNode = parser.parseQuery();
            log.info("SQL语法正确!");
        } catch (SqlParseException e) {
            log.error("SQL语法错误!", e);
            throw new RuntimeException("SQL语法错误:"+e.getMessage());
        }
        return sqlNode;
    }
    public static SqlNode getSqlNodeWithQuoting(String sql) {
        SqlParser parser = SqlParser.create(sql,getQuotingBackTickConfig());
        SqlNode sqlNode;
        try {
            sqlNode = parser.parseQuery();
            log.info("SQL语法正确!");
        } catch (SqlParseException e) {
            log.error("SQL语法错误!", e);
            throw new RuntimeException("SQL语法错误:"+e.getMessage());
        }
        return sqlNode;
    }
    public static String buildPreviewSql(String sql, LocalDate baseDate,String dbType){
        String parseSql = parseDatePlaceholders(sql, baseDate, dbType);
        return appendLimit(parseSql,dbType,100);
    }


    public static String parseDatePlaceholders(String sql, LocalDate baseDate,String dbType){
        SqlNode sqlNode;
        if(ORACLE.equals(dbType)){
            try {
                sqlNode = SqlParser.create(sql,getOracleConfig()).parseQuery();
            } catch (SqlParseException e) {
                log.error("SQL语法错误!", e);
                throw new RuntimeException("SQL语法错误:"+e.getMessage());
            }
        }else{
            sqlNode = getSqlNode(sql);
        }

        return sqlNodeToString(SqlDateParser.parseDatePlaceholders(sqlNode,baseDate),dbType);
    }
    public static SqlNode parseDatePlaceholders(SqlNode sqlNode, LocalDate baseDate){
        return SqlDateParser.parseDatePlaceholders(sqlNode,baseDate);
    }


    public static String sqlNodeToString(SqlNode sqlNode,String dbType) {
        if(dbType.equals(ORACLE)){
            SqlPrettyWriter writer = new SqlPrettyWriter(OracleSqlDialect.DEFAULT);
            sqlNode.unparse(writer, 0, 0);
            return writer.toSqlString().getSql();
        }else{
            return sqlNode.toString();
        }
    }

    public static List<ColumnMetadata> extractColumnMetaData(SqlNode sqlNode) {
        if (sqlNode instanceof SqlSelect) {
            SqlSelect select = (SqlSelect) sqlNode;
            SqlNode from = select.getFrom();
            Map<String, String> tableAliases = extractTableAliases(from);
            List<ColumnMetadata> columnMetadataList = extractSelectClause(select.getSelectList(), tableAliases);
            return columnMetadataList;
        }
        return new ArrayList<>();
    }
    private static List<ColumnMetadata> extractSelectClause(SqlNodeList selectList,Map<String, String> tableAliases) {
        List<ColumnMetadata> columnMetadataList = new ArrayList<>();
        for (SqlNode node : selectList) {
            ColumnMetadata metadata = extractColumnInfo(node);
            if(tableAliases.containsKey(metadata.getTableAlias())){
                metadata.setTableName(tableAliases.get(metadata.getTableAlias()));
            }else{
                metadata.setTableName(tableAliases.get(DEFAULT_TABLE_ALIAS));
                metadata.setTableAlias(DEFAULT_TABLE_ALIAS);
            }
            columnMetadataList.add(metadata);
        }
        return columnMetadataList;
    }

    private static ColumnMetadata extractColumnInfo(SqlNode node) {
        ColumnMetadata metadata = new ColumnMetadata();
        if (node instanceof SqlIdentifier) {
            // 处理简单标识符
            SqlIdentifier identifier = (SqlIdentifier) node;
            if (identifier.names.size() == 2) {
                metadata.setTableAlias(identifier.names.get(0));
                metadata.setColumnName(identifier.names.get(1));
            } else if (identifier.names.size() == 1) {
                metadata.setColumnName(identifier.names.get(0));
            }
        } else if (node instanceof SqlBasicCall) {
            // 处理函数调用或表达式
            SqlBasicCall call = (SqlBasicCall) node;
            // 提取 AS 子句
            SqlNode expression = call.operand(0);
            metadata = extractColumnInfo(expression);
            if (call.getOperator().getKind() == SqlKind.AS) {
                SqlNode alias = call.operand(1);
                metadata.setColumnAlias(alias.toString());
            } else {
                // 非 AS 子句的函数调用
                metadata.setColumnName(call.toString());
            }
        } else {
            // 其他类型的列表达式
            metadata.setColumnName(node.toString());
        }

        if(metadata.getColumnAlias() == null){
            metadata.setColumnAlias(metadata.getColumnName());
        }
        String key = metadata.getColumnName();
        if (metadata.getTableAlias() != null) {
            key = metadata.getTableAlias() +"."+ metadata.getColumnName();
        }
        key = KeyUtil.generateCRC32HexKeyHash(key);
        metadata.setKeyHash(key);

        return metadata;
    }

    private static Map<String, String> extractTableAliases(SqlNode fromNode) {
        Map<String, String> tableAliases = new HashMap<>();

        if (fromNode instanceof SqlJoin) {
            // 如果是 JOIN 子句，递归处理左右部分
            SqlJoin join = (SqlJoin) fromNode;
            tableAliases.putAll(extractTableAliases(join.getLeft()));
            tableAliases.putAll(extractTableAliases(join.getRight()));
        } else if (fromNode instanceof SqlCall && fromNode.getKind() == SqlKind.AS) {
            // 如果是 AS 子句，提取表名和别名
            SqlCall asCall = (SqlCall) fromNode;
            SqlNode tableNode = asCall.operand(0);
            SqlNode aliasNode = asCall.operand(1);

            String tableName = tableNode.toString();
            String aliasName = aliasNode.toString();

            tableAliases.put(aliasName, tableName);
        } else if (fromNode instanceof SqlIdentifier) {
            // 如果是直接的表名，添加表名本身为别名
            String tableName = fromNode.toString();
            tableAliases.put(DEFAULT_TABLE_ALIAS, tableName);
        }

        return tableAliases;
    }

    public static void setColumnType(List<ColumnMetadata> columnMetadataList, LinkedHashMap<String, Integer> columnType) {
        for (ColumnMetadata column : columnMetadataList) {
            String key = StringUtils.isEmpty(column.getColumnAlias()) ? column.getColumnName() : column.getColumnAlias();
            if(columnType.containsKey(key)) {
                column.setDataType(columnType.get(key));
            }else{
                column.setDataType(1);
                log.warn("未找到列类型：{}", key);
            }
        }
    }

    /**
     * 动态创建统计函数节点
     *
     * @param functionName 统计函数名，例如 "COUNT", "SUM"
     * @param columnName   列名（如果为 null，则生成 COUNT(1)）
     * @return SqlBasicCall 节点
     */
    public static SqlBasicCall createDynamicFunction(String functionName, String columnName) {
        // 获取对应的 SqlOperator
        SqlOperator operator = getSqlOperator(functionName);

        // 参数节点
        SqlNode[] arguments;
        if (columnName == null || columnName.isEmpty()) {
            arguments = new SqlNode[]{SqlLiteral.createExactNumeric("1", SqlParserPos.ZERO)};
        } else if (columnName.equals("1")) {
            arguments = new SqlNode[]{SqlLiteral.createExactNumeric("1", SqlParserPos.ZERO)};
        } else if (columnName.equals("*")) {
            arguments = new SqlNode[]{SqlIdentifier.STAR};
        } else {
            // 如 SUM(columnName)
            arguments = new SqlNode[]{new SqlIdentifier(columnName, SqlParserPos.ZERO)};
        }

        // 创建 SqlBasicCall
        return new SqlBasicCall(operator, arguments, SqlParserPos.ZERO);
    }
    /**
     * 根据函数名获取对应的 SqlOperator
     *
     * @param functionName 函数名
     * @return SqlOperator
     */
    private static SqlOperator getSqlOperator(String functionName) {
        switch (functionName.toUpperCase()) {
            case "COUNT":
                return SqlStdOperatorTable.COUNT;
            case "SUM":
                return SqlStdOperatorTable.SUM;
            case "AVG":
                return SqlStdOperatorTable.AVG;
            case "MAX":
                return SqlStdOperatorTable.MAX;
            case "MIN":
                return SqlStdOperatorTable.MIN;
            default:
                throw new IllegalArgumentException("Unsupported function: " + functionName);
        }
    }

    public static String unparseSqlNode(SqlNode sqlNode) {
        SqlPrettyWriter writer = new SqlPrettyWriter(getUnparseConfig());
        sqlNode.unparse(writer, 0, 0);
        return writer.toString();
    }

    // Add this new method for SQL unparsing configuration
    private static SqlWriterConfig getUnparseConfig() {
        SqlDialect dialect = new SqlDialect(SqlDialect.EMPTY_CONTEXT) {
            @Override
            public boolean requiresAliasForFromItems() {
                return false;  // 不强制要求别名
            }
            
            @Override
            public String quoteIdentifier(String val) {
                return val;  // 直接返回标识符，不添加任何引号
            }

            @Override
            public boolean identifierNeedsQuote(String val) {
                return false;  // 表示标识符不需要引号
            }

            @Override
            public void unparseCall(SqlWriter writer, SqlCall call,
                                  int leftPrec, int rightPrec) {
                if (call.getKind() == SqlKind.AS) {
                    SqlCall asCall = call;
                    SqlNode firstOperand = asCall.operand(0);
                    
                    // 检查是否是表别名（FROM子句中的AS）
                    boolean isTableAlias = firstOperand instanceof SqlIdentifier 
                            && (firstOperand.toString().contains(".") || !firstOperand.toString().contains(" "));
                    
                    firstOperand.unparse(writer, leftPrec, rightPrec);
                    
                    if (isTableAlias) {
                        writer.print(" ");  // 表别名只用空格
                    } else {
                        writer.print(" AS ");  // 列别名保留AS关键字
                    }
                    
                    asCall.operand(1).unparse(writer, leftPrec, rightPrec);
                } else {
                    super.unparseCall(writer, call, leftPrec, rightPrec);
                }
            }
        };

        return SqlPrettyWriter.config()
                .withDialect(dialect)
                .withQuoteAllIdentifiers(false)
                .withSelectListItemsOnSeparateLines(true)
                .withIndentation(2);
    }

    private static SqlWriterConfig getOracleUnparseConfig() {
        // 使用Calcite内置的Oracle方言配置
        SqlDialect oracleDialect = SqlDialect.DatabaseProduct.ORACLE.getDialect();
    
        // 创建自定义方言（继承Oracle默认配置）
        SqlDialect customDialect = new SqlDialect(oracleDialect.EMPTY_CONTEXT) {
            @Override
            public boolean requiresAliasForFromItems() {
                // 返回false表示禁止生成表别名的`AS`
                return false;
            }
        };
    
        // 应用配置
        return SqlPrettyWriter.config()
                .withDialect(customDialect)  // 关键：使用自定义Oracle方言
                .withQuoteAllIdentifiers(false)
                .withSelectListItemsOnSeparateLines(true)
                .withIndentation(2);
    }



    
    public static Integer columnTypeConvert(Integer dbColumnType) {
        Integer type = Constants.TYPE_1_STRING;
        //数字类型
        if (dbColumnType == Types.TINYINT) {
            type = Constants.TYPE_2_NUMBER;
        } else if (dbColumnType == Types.SMALLINT) {
            type = Constants.TYPE_2_NUMBER;
        } else if (dbColumnType == Types.INTEGER) {
            type = Constants.TYPE_2_NUMBER;
        } else if (dbColumnType == Types.BIGINT) {
            type = Constants.TYPE_2_NUMBER;
        } else if (dbColumnType == Types.FLOAT) {
            type = Constants.TYPE_2_NUMBER;
        } else if (dbColumnType == Types.REAL) {
            type = Constants.TYPE_2_NUMBER;
        } else if (dbColumnType == Types.DOUBLE) {
            type = Constants.TYPE_2_NUMBER;
        } else if (dbColumnType == Types.NUMERIC) {
            type = Constants.TYPE_2_NUMBER;
        } else if (dbColumnType == Types.DECIMAL) {
            type = Constants.TYPE_2_NUMBER;
        }

        //文字类型
        else if (dbColumnType == Types.CHAR) {
            type = Constants.TYPE_1_STRING;
        } else if (dbColumnType == Types.VARCHAR) {
            type = Constants.TYPE_1_STRING;
        } else if (dbColumnType == Types.LONGVARCHAR) {
            type = Constants.TYPE_1_STRING;
        } else if (dbColumnType == Types.BINARY) {
            type = Constants.TYPE_1_STRING;
        } else if (dbColumnType == Types.VARBINARY) {
            type = Constants.TYPE_1_STRING;
        } else if (dbColumnType == Types.LONGVARBINARY) {
            type = Constants.TYPE_1_STRING;
        } else if (dbColumnType == Types.BIT) {
            type = Constants.TYPE_2_NUMBER;
        }

        //日期类型
        else if (dbColumnType == Types.DATE) {
            type = Constants.TYPE_3_DATE;
        } else if (dbColumnType == Types.TIME) {
            type = Constants.TYPE_3_DATE;
        } else if (dbColumnType == Types.TIMESTAMP) {
            type = Constants.TYPE_3_DATE;
        } else if (dbColumnType == Types.TIME_WITH_TIMEZONE) {
            type = Constants.TYPE_3_DATE;
        } else if (dbColumnType == Types.TIMESTAMP_WITH_TIMEZONE) {
            type = Constants.TYPE_3_DATE;
        }
        return type;
    }

    public static Integer columnTypeConvert(String dbColumnType) {
        String upperCase = dbColumnType.toUpperCase();
        Integer type = DB_TYPE_MAP.get(upperCase);
        if (type == null) {
            type = Constants.TYPE_1_STRING;
        }
        return type;
    }

    public static String appendLimit(String executeSql, String dbType,int limit) {
        switch (dbType.toLowerCase()) {
            case "mysql":
            case "postgresql":
            case "sqlite":
            case "hive":
            case "clickhouse":
                executeSql += String.format(" LIMIT %d", limit);
                break;
            case "oracle":
                executeSql = String.format("SELECT * FROM ( SELECT cc.*, rownum AS rn FROM (%s) cc ) dd WHERE dd.rn <= %d",executeSql,limit);
                break;
            case "sqlserver":
                executeSql = String.format("SELECT TOP %d * FROM (%s)",limit,executeSql);
                break;
            default:
                throw new UnsupportedOperationException("Unsupported database type: " + dbType);
        }
        return executeSql;
    }




}
